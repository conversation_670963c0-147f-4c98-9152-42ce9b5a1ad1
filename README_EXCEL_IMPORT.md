# Chức năng Import Excel vào Typesense

Thư viện TypesenseVectorDB đã được mở rộng để hỗ trợ import dữ liệu từ file Excel với cấu trúc Q&A (Câu hỏi - Đáp án).

## Cấu trúc file Excel yêu cầu

File Excel cần có các cột sau:

| Chức năng | Câu hỏi | Đáp án |
|-----------|---------|--------|
| Cấu hình | Muốn thêm/sửa/xóa nhóm trẻ thì làm như nào? | <PERSON><PERSON> thực hiện, vui lòng thực hiện theo các bước sau: 1. <PERSON><PERSON><PERSON> "Cấu hình"... |
| Cấu hình | Thay đổi logic chính sửa tiền dịch vụ | <PERSON><PERSON> thực hiện, vui lòng thực hiện theo các bước sau: 1. <PERSON><PERSON><PERSON> "<PERSON><PERSON><PERSON> hình"... |

### Yêu cầu:
- **Chức năng**: Tên chức năng/module (có thể để trống)
- **Câu hỏi**: Câu hỏi cần trả lời (bắt buộc)
- **Đáp án**: Câu trả lời tương ứng (bắt buộc)

## Cài đặt dependencies

Trước khi sử dụng, cần cài đặt các thư viện bổ sung:

```bash
pip install pandas openpyxl
```

Hoặc cài đặt từ requirements.txt:

```bash
pip install -r requirements.txt
```

## Sử dụng

### 1. Import cơ bản

```python
from libs.typesense_vector_db import TypesenseVectorDB

# Khởi tạo database
db = TypesenseVectorDB(collection_name="qa_documents")

# Import file Excel
result = db.import_excel_to_typesense(
    file_path="qa_data.xlsx",
    title="Dữ liệu Q&A hệ thống",
    metadata={
        "category": "support",
        "version": "1.0"
    }
)

# Kiểm tra kết quả
if result["success"]:
    print(f"✅ Import thành công {result['imported_documents']} documents")
    print(f"📊 Tổng số dòng: {result['total_rows']}")
    print(f"⏭️ Bỏ qua trùng lặp: {result['skipped_duplicates']}")
else:
    print(f"❌ Lỗi: {result['error']}")
```

### 2. Tìm kiếm sau khi import

```python
# Tìm kiếm documents liên quan
search_result = db.search_similar_documents(
    query="cấu hình nhóm trẻ",
    limit=5,
    threshold=0.3
)

if search_result["success"]:
    for doc in search_result["documents"]:
        metadata = json.loads(doc.get('metadata_json', '{}'))
        print(f"Chức năng: {metadata.get('chuc_nang')}")
        print(f"Câu hỏi: {metadata.get('cau_hoi')}")
        print(f"Similarity: {doc['similarity']:.3f}")
```

### 3. Hệ thống Q&A

```python
# Sử dụng AI để trả lời câu hỏi dựa trên dữ liệu đã import
qa_result = db.search_and_answer(
    question="Làm thế nào để thêm nhóm trẻ mới?",
    limit=3,
    threshold=0.3
)

if qa_result["success"]:
    print(f"Câu trả lời: {qa_result['answer']}")
    print(f"Độ tin cậy: {qa_result['confidence']:.3f}")
    print(f"Nguồn tham khảo: {len(qa_result['sources'])} documents")
```

## API Reference

### `import_excel_to_typesense(file_path, title=None, metadata=None)`

Import dữ liệu từ file Excel vào Typesense.

**Parameters:**
- `file_path` (str): Đường dẫn đến file Excel
- `title` (str, optional): Tiêu đề cho tài liệu
- `metadata` (dict, optional): Metadata bổ sung

**Returns:**
```python
{
    "success": True/False,
    "total_rows": int,           # Tổng số dòng trong Excel
    "imported_documents": int,   # Số documents đã import thành công
    "skipped_duplicates": int,   # Số documents bị bỏ qua do trùng lặp
    "file_path": str,           # Đường dẫn file
    "title": str,               # Tiêu đề
    "error": str                # Thông báo lỗi (nếu có)
}
```

## Tính năng

### ✅ Đã hỗ trợ:
- Import file Excel (.xlsx, .xls)
- Kiểm tra cấu trúc file (các cột bắt buộc)
- Loại bỏ dòng trống tự động
- Tránh trùng lặp dữ liệu (dựa trên content hash)
- Tạo embedding cho từng Q&A pair
- Metadata mở rộng (lưu trữ chức năng, câu hỏi, đáp án riêng biệt)
- Batch import để tối ưu hiệu suất
- Tìm kiếm ngữ nghĩa
- Hệ thống Q&A với AI

### 🔄 Cấu trúc dữ liệu lưu trữ:

Mỗi dòng Excel sẽ được chuyển thành một document với:

```python
{
    "id": "uuid",
    "content": "Chức năng: ...\nCâu hỏi: ...\nĐáp án: ...",
    "title": "QA - Chức năng",
    "source_file": "qa_data.xlsx",
    "chunk_index": 0,
    "metadata_json": {
        "chuc_nang": "Cấu hình",
        "cau_hoi": "Muốn thêm/sửa/xóa nhóm trẻ thì làm như nào?",
        "dap_an": "Để thực hiện, vui lòng...",
        "row_index": 1,
        "data_type": "qa_pair"
    },
    "embedding": [0.1, 0.2, ...],
    "created_at": timestamp,
    "content_hash": "md5_hash"
}
```

## Test và Demo

Chạy file test để kiểm tra chức năng:

```bash
python test_excel_import.py
```

Test sẽ:
1. Tạo file Excel mẫu
2. Test kết nối Typesense
3. Import dữ liệu từ Excel
4. Test tìm kiếm
5. Test hệ thống Q&A
6. Cleanup file test

## Lưu ý

1. **File Excel phải có đúng cấu trúc**: Các cột "Chức năng", "Câu hỏi", "Đáp án"
2. **Dữ liệu không được để trống**: Cột "Câu hỏi" và "Đáp án" là bắt buộc
3. **Tránh trùng lặp**: Hệ thống tự động kiểm tra và bỏ qua dữ liệu trùng lặp
4. **Hiệu suất**: Import theo batch để tối ưu với file lớn
5. **Encoding**: Hỗ trợ tiếng Việt và các ký tự Unicode

## Troubleshooting

### Lỗi thường gặp:

1. **"File Excel thiếu các cột"**: Kiểm tra tên cột chính xác
2. **"Không thể đọc file Excel"**: Kiểm tra file có bị lỗi không
3. **"Không có dữ liệu hợp lệ"**: Kiểm tra dữ liệu trong cột Câu hỏi và Đáp án
4. **Lỗi kết nối Typesense**: Kiểm tra cấu hình trong file .env

### Debug:

Bật debug để xem chi tiết quá trình import:

```python
import logging
logging.basicConfig(level=logging.DEBUG)
```
