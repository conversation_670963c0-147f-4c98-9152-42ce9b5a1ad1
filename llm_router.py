import random
import threading
from datetime import datetime, date
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from langchain_community.chat_models import ChatOpenAI
from langchain_google_genai import ChatGoogleGenerativeAI

# Import database manager
try:
    from database_manager import DatabaseManager, create_database_config_from_env
    DATABASE_AVAILABLE = True
except ImportError:
    DATABASE_AVAILABLE = False
    print("⚠️  Database manager không khả dụng. Sẽ sử dụng file JSON như cũ.")


@dataclass
class LLMConfig:
    """Cấu hình cho mỗi LLM"""
    name: str
    provider: str  # 'openai', 'gemini', 'groq', 'github', 'localai'
    model: str
    api_base_url: str
    api_token: str
    max_calls: int
    remaining_calls: int
    max_input_tokens: int = 3500  # Giới hạn input tokens cho mỗi chunk
    temperature: float = 0.0
    enabled: bool = True
    
    def to_dict(self):
        """Convert to dictionary for JSON serialization"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data: dict):
        """Create from dictionary"""
        return cls(**data)


class LLMRouter:
    """Router để quản lý và chọn ngẫu nhiên giữa các LLM"""
    
    def __init__(self, use_database: bool = True, max_prompt_length: int = None, max_response_length: int = None):
        """
        Khởi tạo router

        Args:
            use_database (bool): Có sử dụng database không (mặc định True)
            max_prompt_length (int): Giới hạn độ dài prompt khi lưu database (None = không giới hạn)
            max_response_length (int): Giới hạn độ dài response khi lưu database (None = không giới hạn)
        """
        self.llm_configs: List[LLMConfig] = []
        self.lock = threading.Lock()  # Thread safety

        # Database configuration - bắt buộc phải có database
        if not DATABASE_AVAILABLE:
            raise Exception("❌ Database manager không khả dụng. Vui lòng cài đặt và cấu hình database.")

        if not use_database:
            raise Exception("❌ Router chỉ hỗ trợ load config từ database. Vui lòng set use_database=True.")

        self.use_database = True
        self.db_manager = None

        # Text length limits for database storage
        self.max_prompt_length = max_prompt_length
        self.max_response_length = max_response_length

        try:
            self.db_manager = DatabaseManager(create_database_config_from_env())
            print("✅ Database manager được khởi tạo thành công")
        except Exception as e:
            raise Exception(f"❌ Không thể khởi tạo database: {e}")

        self.load_config()
    
    def load_config(self):
        """Load cấu hình từ database"""
        if not self.db_manager:
            raise Exception("❌ Database manager không khả dụng")

        try:
            db_configs = self.db_manager.get_all_llm_configs()
            if db_configs:
                # Convert từ database format sang LLMConfig objects
                self.llm_configs = []
                for db_config in db_configs:
                    llm_config = LLMConfig(
                        name=db_config['name'],
                        provider=db_config['provider'],
                        model=db_config['model'],
                        api_base_url=db_config['api_base_url'],
                        api_token=db_config['api_token'],
                        max_calls=db_config['max_calls'],
                        remaining_calls=self.get_remaining_calls_from_database(db_config['name']),
                        max_input_tokens=db_config['max_input_tokens'],
                        temperature=db_config['temperature'],
                        enabled=db_config['enabled']
                    )
                    self.llm_configs.append(llm_config)

                print(f"✅ Đã load {len(self.llm_configs)} LLM configs từ database")
            else:
                print("⚠️  Database trống, sẽ tạo cấu hình mặc định")
                #self.create_default_config()

        except Exception as e:
            print(f"❌ Lỗi load từ database: {e}")
            # Nếu database có vấn đề, tạo config mặc định
            print("⚠️  Sẽ tạo cấu hình mặc định")
            #self.create_default_config()    
    
    
    def get_available_llms(self) -> List[LLMConfig]:
        """Lấy danh sách LLM còn lượt call và được enable"""
        available_llms = []
        
        for config in self.llm_configs:
            if not config.enabled:
                continue
            
            # Lấy remaining calls từ database
            remaining_calls = self.get_remaining_calls_from_database(config.name)
            
            if remaining_calls > 0:
                # Cập nhật remaining_calls cho config
                config.remaining_calls = remaining_calls
                available_llms.append(config)
        
        return available_llms
    
    def select_random_llm(self) -> Optional[LLMConfig]:
        """Chọn ngẫu nhiên một LLM có thể sử dụng"""
        available_llms = self.get_available_llms()
        
        if not available_llms:
            print("❌ Không có LLM nào khả dụng!")
            return None
        
        # Weighted random selection based on remaining calls
        weights = [config.remaining_calls for config in available_llms]
        selected = random.choices(available_llms, weights=weights, k=1)[0]
        
        print(f"🎯 Đã chọn LLM: {selected.name} (còn {selected.remaining_calls} lượt)")
        return selected
    
    def create_llm_instance(self, config: LLMConfig):
        """Tạo instance LLM từ config"""
        try:
            if config.provider == 'openai':
                return ChatOpenAI(
                    model=config.model,
                    temperature=config.temperature,
                    openai_api_base=config.api_base_url,
                    openai_api_key=config.api_token
                )
            elif config.provider == 'gemini':
                return ChatGoogleGenerativeAI(
                    model=config.model,
                    temperature=config.temperature,
                    google_api_key=config.api_token,
                    convert_system_message_to_human=True
                )
            elif config.provider in ['groq', 'github', 'localai']:
                print(config.api_base_url+ " -> "+config.api_token+"-> "+config.model)
                return ChatOpenAI(
                    model=config.model,
                    temperature=config.temperature,
                    openai_api_base=config.api_base_url,
                    openai_api_key=config.api_token
                )
            else:
                raise Exception(f"Provider không được hỗ trợ: {config.provider}")
                
        except Exception as e:
            print(f"❌ Lỗi tạo LLM instance cho {config.name}: {e}")
            return None
    
    def make_request(self, prompt: str, **kwargs) -> Optional[Dict[str, Any]]:
        """
        Gửi request đến một LLM được chọn ngẫu nhiên
        
        Args:
            prompt (str): Prompt để gửi
            **kwargs: Các tham số khác cho LLM
            
        Returns:
            Dict hoặc None: Kết quả từ LLM hoặc None nếu thất bại
        """
        start_time = datetime.now()
        selected_config = None
        
        with self.lock:
            selected_config = self.select_random_llm()
            
            if not selected_config:
                return None
            
            # Giảm số lượt call trong memory
            selected_config.remaining_calls -= 1
            #self.save_config()
            
            print(f"📤 Gửi request đến {selected_config.name} (còn {selected_config.remaining_calls} lượt)")
        
        # Chuẩn bị log data
        log_data = {
            'llm_name': selected_config.name,
            'provider': selected_config.provider,
            'model': selected_config.model,
            'prompt_text': prompt[:self.max_prompt_length] if self.max_prompt_length else prompt,
            'request_data': kwargs,
            'input_tokens': 0,
            'output_tokens': 0,
            'total_tokens': 0,
            'success': False,
            'error_message': '',
            'processing_time_ms': 0,
            'response_text': '',
            'response_data': {}
        }
        
        try:
            # Tạo LLM instance
            llm = self.create_llm_instance(selected_config)
            if not llm:
                raise Exception("Không thể tạo LLM instance")
            
            # Gửi request
            from langchain.prompts import ChatPromptTemplate
            from langchain.chains import LLMChain
            
            chat_prompt = ChatPromptTemplate.from_template(prompt)
            chain = LLMChain(llm=llm, prompt=chat_prompt)
            
            result = chain.invoke(kwargs)
            
            # Tính thời gian xử lý
            processing_time = datetime.now() - start_time
            processing_time_ms = int(processing_time.total_seconds() * 1000)
            
            # Cập nhật log data với kết quả thành công
            log_data.update({
                'success': True,
                'response_text': result['text'][:self.max_response_length] if self.max_response_length else result['text'],
                'response_data': {'full_result': str(result)[:1000] if len(str(result)) > 1000 else str(result)},
                'processing_time_ms': processing_time_ms
            })
            
            # Log vào database
            try:
                self.db_manager.log_request(log_data)
            except Exception as e:
                print(f"⚠️  Lỗi log request vào database: {e}")
            
            return {
                'success': True,
                'llm_name': selected_config.name,
                'provider': selected_config.provider,
                'model': selected_config.model,
                'remaining_calls': selected_config.remaining_calls,
                'response': result['text'],
                'timestamp': datetime.now().isoformat(),
                'processing_time_ms': processing_time_ms
            }
            
        except Exception as e:
            print(f"❌ Lỗi khi gửi request đến {selected_config.name}: {e}")
            
            # Tính thời gian xử lý cho trường hợp lỗi
            processing_time = datetime.now() - start_time
            processing_time_ms = int(processing_time.total_seconds() * 1000)
            
            # Hoàn lại số lượt call nếu thất bại
            with self.lock:
                selected_config.remaining_calls += 1
                #self.save_config()
            
            # Cập nhật log data với lỗi
            log_data.update({
                'success': False,
                'error_message': str(e)[:1000] if len(str(e)) > 1000 else str(e),
                'processing_time_ms': processing_time_ms
            })
            
            # Log vào database ngay cả khi lỗi
            try:
                self.db_manager.log_request(log_data)
            except Exception as log_error:
                print(f"⚠️  Lỗi log request lỗi vào database: {log_error}")
            
            return {
                'success': False,
                'llm_name': selected_config.name,
                'provider': selected_config.provider,
                'model': selected_config.model,
                'error': str(e),
                'timestamp': datetime.now().isoformat(),
                'processing_time_ms': processing_time_ms
            }
    
    def get_status(self) -> Dict[str, Any]:
        """Lấy trạng thái của tất cả LLM"""
        total_calls = sum(config.remaining_calls for config in self.llm_configs)
        available_count = len(self.get_available_llms())
        
        status = {
            'total_llms': len(self.llm_configs),
            'available_llms': available_count,
            'total_remaining_calls': total_calls,
            'llms': []
        }
        
        for config in self.llm_configs:
            llm_status = {
                'name': config.name,
                'provider': config.provider,
                'model': config.model,
                'max_calls': config.max_calls,
                'remaining_calls': config.remaining_calls,
                'enabled': config.enabled,
                'usage_percentage': ((config.max_calls - config.remaining_calls) / config.max_calls * 100) if config.max_calls > 0 else 0
            }
            status['llms'].append(llm_status)
        
        return status
    
    def print_status(self):
        """In trạng thái ra console"""
        status = self.get_status()
        
        print("\n" + "="*60)
        print("🤖 TRẠNG THÁI LLM ROUTER")
        print("="*60)
        print(f"📊 Tổng số LLM: {status['total_llms']}")
        print(f"✅ LLM khả dụng: {status['available_llms']}")
        print(f"🔢 Tổng lượt call còn lại: {status['total_remaining_calls']}")
        print("\n📋 CHI TIẾT TỪNG LLM:")
        
        for llm in status['llms']:
            status_icon = "✅" if llm['enabled'] and llm['remaining_calls'] > 0 else "❌"
            usage = llm['usage_percentage']
            
            print(f"\n{status_icon} {llm['name']}")
            print(f"   Provider: {llm['provider']}")
            print(f"   Model: {llm['model']}")
            print(f"   Còn lại: {llm['remaining_calls']}/{llm['max_calls']} lượt")
            print(f"   Đã dùng: {usage:.1f}%")
            print(f"   Trạng thái: {'Khả dụng' if llm['enabled'] and llm['remaining_calls'] > 0 else 'Không khả dụng'}")
        
        print("="*60)
    
    def reset_all_calls(self):
        """Reset tất cả số lượt call về max"""
        with self.lock:
            for config in self.llm_configs:
                config.remaining_calls = config.max_calls
            #self.save_config()
            
            # Reset trong database
            self.reset_daily_calls_database()
                
            print("✅ Đã reset tất cả số lượt call")

    
    def get_max_input_tokens(self, llm_name: str = None) -> int:
        """
        Lấy max_input_tokens cho LLM cụ thể hoặc tính toán giá trị tối ưu
        
        Args:
            llm_name (str): Tên LLM cụ thể, nếu None sẽ tính trung bình của các LLM khả dụng
            
        Returns:
            int: Giá trị max_input_tokens
        """
        if llm_name:
            # Tìm LLM cụ thể
            for config in self.llm_configs:
                if config.name == llm_name:
                    return config.max_input_tokens
            # Nếu không tìm thấy, trả về giá trị mặc định
            return 3500
        
        # Tính toán dựa trên các LLM khả dụng
        available_llms = self.get_available_llms()
        if not available_llms:
            return 3500  # Giá trị mặc định
        
        # Lấy giá trị trung bình (có thể thay đổi logic này)
        total_tokens = sum(config.max_input_tokens for config in available_llms)
        avg_tokens = total_tokens // len(available_llms)
        
        return avg_tokens


    
    def get_remaining_calls_from_database(self, llm_name: str) -> int:
        """Lấy remaining calls từ database"""
        if not self.db_manager:
            return 0
        
        try:
            return self.db_manager.get_daily_remaining_calls(llm_name)
        except Exception as e:
            print(f"⚠️  Lỗi lấy remaining calls từ database: {e}")
            return 0
    
    def reset_daily_calls_database(self, target_date: date = None) -> bool:
        """Reset daily calls trong database"""
        if not self.db_manager:
            return False

        try:
            return self.db_manager.reset_daily_calls(target_date)
        except Exception as e:
            print(f"❌ Lỗi reset daily calls trong database: {e}")
            return False
    
    def get_database_stats(self, days: int = 7) -> Dict[str, Any]:
        """Lấy thống kê từ database"""
        if not self.db_manager:
            return {}

        try:
            from datetime import timedelta
            start_date = date.today() - timedelta(days=days)
            return self.db_manager.get_request_stats(start_date, date.today())
        except Exception as e:
            print(f"❌ Lỗi lấy thống kê database: {e}")
            return {}
    
    def cleanup_old_database_logs(self, days_to_keep: int = 30) -> bool:
        """Xóa log cũ trong database"""
        if not self.db_manager:
            return False

        try:
            return self.db_manager.cleanup_old_logs(days_to_keep)
        except Exception as e:
            print(f"❌ Lỗi cleanup database logs: {e}")
            return False

# Utility functions
def create_router_from_env(max_prompt_length: int = None, max_response_length: int = None):
    """Tạo router từ environment variables"""
    import os

    # Đọc từ environment variables nếu không được truyền vào
    if max_prompt_length is None:
        env_prompt_length = os.getenv('MAX_PROMPT_LENGTH', '').strip()
        max_prompt_length = int(env_prompt_length) if env_prompt_length.isdigit() else None

    if max_response_length is None:
        env_response_length = os.getenv('MAX_RESPONSE_LENGTH', '').strip()
        max_response_length = int(env_response_length) if env_response_length.isdigit() else None

    router = LLMRouter(
        max_prompt_length=max_prompt_length,
        max_response_length=max_response_length
    )

    return router

